import { useCallback, useRef, useEffect } from 'react';

interface UseMarkAsReadOptions {
  conversationId: string | null;
  userId: string;
  debounceMs?: number;
  sendReadReceipt?: (conversationId: string, messageId?: string) => boolean;
  markMessagesAsRead?: (conversationId: string) => boolean;
  isWebSocketConnected?: boolean;
}

export const useMarkAsRead = ({
  conversationId,
  userId,
  debounceMs = 1000,
  sendReadReceipt,
  markMessagesAsRead,
  isWebSocketConnected = true,
}: UseMarkAsReadOptions) => {
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);
  const lastReadMessageRef = useRef<string | null>(null);

  const debouncedMarkAsRead = useCallback(() => {
    if (!conversationId || !markMessagesAsRead) return;

    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      if (!isWebSocketConnected) {
        console.debug('Skipping mark as read - WebSocket not connected');
        return;
      }

      try {
        const markAsReadSent = markMessagesAsRead(conversationId);

        if (!markAsReadSent) {
          console.debug(
            'Could not mark messages as read - WebSocket may not be connected'
          );
        }
      } catch (err) {
        console.error('Error marking messages as read:', err);
      }
    }, debounceMs);
  }, [conversationId, markMessagesAsRead, debounceMs, isWebSocketConnected]);

  const markAsReadImmediately = useCallback(() => {
    if (!conversationId || !markMessagesAsRead) return;

    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    if (!isWebSocketConnected) {
      console.debug('Skipping mark as read - WebSocket not connected');
      return;
    }

    try {
      const markAsReadSent = markMessagesAsRead(conversationId);

      if (!markAsReadSent) {
        console.debug(
          'Could not mark messages as read - WebSocket may not be connected'
        );
      }
    } catch (err) {
      console.error('Error marking messages as read:', err);
    }
  }, [conversationId, markMessagesAsRead, isWebSocketConnected]);

  const setupMessageObserver = useCallback(
    (messageElements: NodeListOf<Element>) => {
      if (!conversationId || !userId || !sendReadReceipt) return;

      if (observerRef.current) {
        observerRef.current.disconnect();
      }

      observerRef.current = new IntersectionObserver(
        entries => {
          let shouldMarkAsRead = false;
          let lastVisibleMessageId: string | null = null;

          entries.forEach(entry => {
            if (entry.isIntersecting) {
              const messageElement = entry.target as HTMLElement;
              const senderId = messageElement.dataset.senderId;
              const messageId = messageElement.dataset.messageId;

              if (senderId && senderId !== userId) {
                shouldMarkAsRead = true;
                if (messageId) {
                  lastVisibleMessageId = messageId;
                }
              }
            }
          });

          if (shouldMarkAsRead) {
            if (
              lastVisibleMessageId &&
              lastVisibleMessageId !== lastReadMessageRef.current
            ) {
              sendReadReceipt(conversationId, lastVisibleMessageId);
              lastReadMessageRef.current = lastVisibleMessageId;
            }

            debouncedMarkAsRead();
          }
        },
        {
          threshold: 0.5,
          rootMargin: '0px 0px -50px 0px',
        }
      );

      messageElements.forEach(element => {
        observerRef.current?.observe(element);
      });
    },
    [conversationId, userId, sendReadReceipt, debouncedMarkAsRead]
  );

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, []);

  return {
    markAsReadImmediately,
    debouncedMarkAsRead,
    setupMessageObserver,
  };
};
