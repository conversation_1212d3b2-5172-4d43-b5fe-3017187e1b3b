import { useState, useEffect, useRef, useCallback } from 'react';

export type WebSocketMessageType =
  | 'TEXT_MESSAGE'
  | 'new_message'
  | 'TYPING_INDICATOR'
  | 'READ_RECEIPT'
  | 'CONVERSATION_READ'
  | 'JOIN_CONVERSATION'
  | 'LEAVE_CONVERSATION'
  | 'CONNECTION_ERROR'
  | 'CONNECTION_SUCCESS'
  | 'GET_CONVERSATIONS'
  | 'CONVERSATIONS_RESPONSE'
  | 'GET_CONVERSATION'
  | 'CONVERSATION_RESPONSE'
  | 'CREATE_CONVERSATION'
  | 'CONVERSATION_CREATED'
  | 'GET_MESSAGES'
  | 'MESSAGES_RESPONSE'
  | 'SEARCH_MESSAGES'
  | 'SEARCH_RESPONSE'
  | 'MARK_MESSAGES_READ'
  | 'MESSAGES_MARKED_READ'
  | 'UPDATE_CONVERSATION_STATUS'
  | 'CONVERSATION_STATUS_UPDATED'
  | 'GET_UNREAD_COUNT'
  | 'UNREAD_COUNT_RESPONSE'
  | 'GET_NURSE_INFO'
  | 'NURSE_INFO_RESPONSE'
  | 'MESSAGE_SENT'
  | 'MESSAGE_DELIVERED'
  | 'MESSAGE_READ'
  | 'MESSAGE_STATUS_UPDATE'
  | 'MESSAGE_ERROR'
  | 'PONG'
  | 'ERROR';

export interface WebSocketMessage {
  type: WebSocketMessageType;
  conversationId?: string;
  senderId?: string;
  senderType?: 'nurse' | 'patient';
  senderName?: string;
  content?: string;
  timestamp?: string;
  metadata?: Record<string, unknown>;

  requestId?: string;
  data?: unknown;
  error?: string;
  success?: boolean;

  page?: number;
  limit?: number;
  status?: 'active' | 'inactive' | 'archived';
  query?: string;
  patientId?: string;
  patientName?: string;
  initialMessage?: string;
  messageId?: string;
}

export interface WebSocketStatus {
  connected: boolean;
  connecting: boolean;
  error: string | null;
  reconnectAttempt: number;
}

interface UseWebSocketOptions {
  url: string;
  token: string;
  userId: string;
  userType: 'nurse' | 'patient';
  userName: string;
  enabled?: boolean;
  autoReconnect?: boolean;
  reconnectInterval?: number;
  maxReconnectAttempts?: number;
  onOpen?: (event: WebSocketEventMap['open']) => void;
  onClose?: (event: WebSocketEventMap['close']) => void;
  onError?: (event: WebSocketEventMap['error']) => void;
  onMessage?: (message: WebSocketMessage) => void;
}

const useWebSocket = ({
  url,
  token,
  userId,
  userType,
  userName,
  enabled = true,
  autoReconnect = true,
  reconnectInterval = 3000,
  maxReconnectAttempts = 5,
  onOpen,
  onClose,
  onError,
  onMessage,
}: UseWebSocketOptions) => {
  const socketRef = useRef<WebSocket | null>(null);

  const [status, setStatus] = useState<WebSocketStatus>({
    connected: false,
    connecting: false,
    error: null,
    reconnectAttempt: 0,
  });

  const [activeConversations, setActiveConversations] = useState<string[]>([]);

  const reconnectTimerRef = useRef<NodeJS.Timeout | null>(null);

  const connectionTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const lastMessageTimestampRef = useRef<Record<string, number>>({});

  const [typingUsers, setTypingUsers] = useState<
    Record<string, { userId: string; userName: string; timestamp: number }>
  >({});

  const typingTimeoutRef = useRef<Record<string, NodeJS.Timeout>>({});

  const onOpenRef = useRef(onOpen);
  const onCloseRef = useRef(onClose);
  const onErrorRef = useRef(onError);
  const onMessageRef = useRef(onMessage);
  const statusRef = useRef(status);
  const urlRef = useRef(url);
  const tokenRef = useRef(token);
  const userIdRef = useRef(userId);
  const userTypeRef = useRef(userType);
  const userNameRef = useRef(userName);
  const enabledRef = useRef(enabled);
  const autoReconnectRef = useRef(autoReconnect);
  const reconnectIntervalRef = useRef(reconnectInterval);
  const maxReconnectAttemptsRef = useRef(maxReconnectAttempts);
  const activeConversationsRef = useRef(activeConversations);

  useEffect(() => {
    onOpenRef.current = onOpen;
    onCloseRef.current = onClose;
    onErrorRef.current = onError;
    onMessageRef.current = onMessage;
    urlRef.current = url;
    tokenRef.current = token;
    userIdRef.current = userId;
    userTypeRef.current = userType;
    userNameRef.current = userName;
    enabledRef.current = enabled;
    autoReconnectRef.current = autoReconnect;
    reconnectIntervalRef.current = reconnectInterval;
    maxReconnectAttemptsRef.current = maxReconnectAttempts;
  }, [
    onOpen,
    onClose,
    onError,
    onMessage,
    url,
    token,
    userId,
    userType,
    userName,
    enabled,
    autoReconnect,
    reconnectInterval,
    maxReconnectAttempts,
  ]);

  useEffect(() => {
    statusRef.current = status;
  }, [status]);

  useEffect(() => {
    activeConversationsRef.current = activeConversations;
  }, [activeConversations]);

  const clearTimers = useCallback(() => {
    if (reconnectTimerRef.current) {
      clearTimeout(reconnectTimerRef.current);
      reconnectTimerRef.current = null;
    }

    if (connectionTimeoutRef.current) {
      clearTimeout(connectionTimeoutRef.current);
      connectionTimeoutRef.current = null;
    }

    Object.values(typingTimeoutRef.current).forEach(timeout => {
      clearTimeout(timeout);
    });
  }, []);

  const connect = useCallback(() => {
    console.debug(
      'Connect called, enabled:',
      enabledRef.current,
      'current status:',
      statusRef.current,
      'socket state:',
      socketRef.current?.readyState
    );
    if (
      !enabledRef.current ||
      statusRef.current.connecting ||
      (socketRef.current &&
        (socketRef.current.readyState === WebSocket.OPEN ||
          socketRef.current.readyState === WebSocket.CONNECTING))
    ) {
      console.debug(
        'Connect aborted - disabled, already connecting or connected'
      );
      return;
    }

    clearTimers();

    setStatus(prev => ({ ...prev, connecting: true, error: null }));

    try {
      if (!urlRef.current) {
        throw new Error('WebSocket URL is required');
      }

      if (!tokenRef.current) {
        throw new Error('Authentication token is required');
      }

      if (!userIdRef.current) {
        throw new Error('User ID is required');
      }

      let wsUrl: string;
      try {
        wsUrl = `${urlRef.current}?token=${encodeURIComponent(tokenRef.current)}&userId=${encodeURIComponent(userIdRef.current)}&userType=${userTypeRef.current}`;

        const debugUrl = wsUrl.replace(
          /token=([^&]{1,10}).*?(&|$)/,
          'token=$1...$2'
        );
        console.debug('Connecting to WebSocket:', debugUrl);
      } catch (urlError) {
        throw new Error(
          `Invalid WebSocket URL or parameters: ${urlError.message}`
        );
      }

      console.debug('Creating new WebSocket connection...');
      socketRef.current = new WebSocket(wsUrl);

      connectionTimeoutRef.current = setTimeout(() => {
        if (statusRef.current.connecting && !statusRef.current.connected) {
          console.error('WebSocket connection timeout after 10 seconds');
          if (socketRef.current) {
            socketRef.current.close();
          }

          setStatus(prev => ({
            ...prev,
            connecting: false,
            error: 'Connection timeout after 10 seconds',
          }));

          if (
            autoReconnectRef.current &&
            statusRef.current.reconnectAttempt < maxReconnectAttemptsRef.current
          ) {
            scheduleReconnect();
          }
        }
      }, 10000);

      socketRef.current.onopen = event => {
        if (connectionTimeoutRef.current) {
          clearTimeout(connectionTimeoutRef.current);
          connectionTimeoutRef.current = null;
        }

        setStatus({
          connected: true,
          connecting: false,
          error: null,
          reconnectAttempt: 0,
        });

        activeConversationsRef.current.forEach(conversationId => {
          joinConversation(conversationId);
        });

        if (onOpenRef.current) {
          onOpenRef.current(event);
        }
      };

      socketRef.current.onclose = event => {
        if (connectionTimeoutRef.current) {
          clearTimeout(connectionTimeoutRef.current);
          connectionTimeoutRef.current = null;
        }

        setStatus(prev => ({
          ...prev,
          connected: false,
          connecting: false,
          error:
            event.code === 1000
              ? null
              : `Connection closed: ${event.code} ${event.reason || ''}`,
        }));

        if (onCloseRef.current) {
          onCloseRef.current(event);
        }

        if (
          autoReconnectRef.current &&
          event.code !== 1000 &&
          statusRef.current.reconnectAttempt < maxReconnectAttemptsRef.current
        ) {
          scheduleReconnect();
        } else if (
          statusRef.current.reconnectAttempt >= maxReconnectAttemptsRef.current
        ) {
          setStatus(prev => ({
            ...prev,
            error:
              'Max reconnection attempts reached. Please refresh the page.',
          }));
        }
      };

      socketRef.current.onerror = event => {
        const ws = event.target as WebSocket;
        const readyState = ws?.readyState;

        if (readyState !== WebSocket.CLOSED) {
          console.error('WebSocket error occurred:', {
            type: event.type,
            readyState,
            url: ws?.url?.replace(
              /token=([^&]{1,10}).*?(&|$)/,
              'token=$1...$2'
            ),
          });
        }

        let errorMessage = 'WebSocket connection error';
        if (readyState === WebSocket.CONNECTING) {
          errorMessage = 'Failed to establish WebSocket connection';
        } else if (readyState === WebSocket.CLOSING) {
          errorMessage = 'WebSocket connection is closing';
        }

        setStatus(prev => ({
          ...prev,
          error: errorMessage,
        }));

        if (onErrorRef.current) {
          onErrorRef.current(event);
        }
      };

      socketRef.current.onmessage = event => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data);

          switch (message.type) {
            case 'CONNECTION_SUCCESS':
              console.debug('Authentication successful, session established');
              break;

            case 'TEXT_MESSAGE':
            case 'new_message':
              if (onMessageRef.current) {
                onMessageRef.current(message);
              }
              break;

            case 'TYPING_INDICATOR':
              if (
                message.conversationId &&
                message.senderId &&
                message.senderName
              ) {
                if (typingTimeoutRef.current[message.senderId]) {
                  clearTimeout(typingTimeoutRef.current[message.senderId]);
                }

                setTypingUsers(prev => ({
                  ...prev,
                  [message.senderId]: {
                    userId: message.senderId,
                    userName: message.senderName || 'Unknown',
                    timestamp: Date.now(),
                  },
                }));

                typingTimeoutRef.current[message.senderId] = setTimeout(() => {
                  setTypingUsers(prev => {
                    const newState = { ...prev };
                    delete newState[message.senderId!];
                    return newState;
                  });
                }, 3000);
              }
              break;

            case 'READ_RECEIPT':
              break;

            case 'CONVERSATION_READ':
              break;

            case 'PONG':
              console.debug('🏓 Pong received from server');
              break;

            case 'CONNECTION_ERROR':
              console.error(
                'Server reported connection error:',
                message.content
              );
              setStatus(prev => ({
                ...prev,
                error: message.content || 'Server connection error',
              }));
              break;

            default:
              if (onMessageRef.current) {
                onMessageRef.current(message);
              }
          }
        } catch (error) {
          console.error(
            'Error parsing WebSocket message:',
            error,
            'Raw data:',
            event.data
          );
        }
      };
    } catch (error) {
      console.error('Failed to create WebSocket connection:', error);

      setStatus({
        connected: false,
        connecting: false,
        error: error.message || 'Failed to create WebSocket connection',
        reconnectAttempt: statusRef.current.reconnectAttempt,
      });

      if (
        autoReconnectRef.current &&
        statusRef.current.reconnectAttempt < maxReconnectAttemptsRef.current
      ) {
        scheduleReconnect();
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [clearTimers]);

  const scheduleReconnect = useCallback(() => {
    const nextAttempt = statusRef.current.reconnectAttempt + 1;

    setStatus(prev => ({
      ...prev,
      reconnectAttempt: nextAttempt,
    }));

    if (reconnectTimerRef.current) {
      clearTimeout(reconnectTimerRef.current);
    }

    const baseDelay = reconnectIntervalRef.current * Math.pow(1.5, nextAttempt);
    const jitter = Math.random() * 0.3 * baseDelay;
    const delay = Math.min(baseDelay + jitter, 60000);

    console.debug(
      `Scheduling reconnection attempt ${nextAttempt} in ${Math.round(delay / 1000)}s`
    );

    reconnectTimerRef.current = setTimeout(() => {
      console.debug(
        `Attempting reconnection ${nextAttempt}/${maxReconnectAttemptsRef.current}`
      );
      connect();
    }, delay);
  }, [connect]);

  const disconnect = useCallback(() => {
    clearTimers();

    if (socketRef.current) {
      try {
        const currentState = socketRef.current.readyState;

        if (currentState === WebSocket.OPEN) {
          console.debug('Closing open WebSocket connection...');
          socketRef.current.close(1000, 'User initiated disconnect');
        } else if (currentState === WebSocket.CONNECTING) {
          console.debug('Aborting connecting WebSocket...');

          const originalOnError = socketRef.current.onerror;

          socketRef.current.onopen = () => {
            console.debug(
              'WebSocket opened during disconnect, closing immediately...'
            );
            if (socketRef.current) {
              socketRef.current.close(1000, 'Disconnected during connection');
            }
          };

          socketRef.current.onerror = event => {
            console.debug('WebSocket error during disconnect, cleaning up...');

            if (originalOnError) {
              originalOnError.call(socketRef.current, event);
            }
          };

          setTimeout(() => {
            if (
              socketRef.current &&
              socketRef.current.readyState === WebSocket.CONNECTING
            ) {
              console.debug('Force closing stuck connecting WebSocket...');
              socketRef.current.close();
            }
          }, 1000);
        } else {
          console.debug(
            'WebSocket already closed or closing, state:',
            currentState
          );
        }
      } catch (error) {
        console.error('Error closing WebSocket:', error);
      }

      setTimeout(() => {
        socketRef.current = null;
      }, 100);
    }

    setStatus({
      connected: false,
      connecting: false,
      error: null,
      reconnectAttempt: 0,
    });

    setActiveConversations([]);
    setTypingUsers({});
  }, [clearTimers]);

  const sendMessage = useCallback(
    (message: WebSocketMessage) => {
      if (!socketRef.current) {
        console.debug(
          'WebSocket is not initialized, message not sent:',
          message.type
        );
        return false;
      }

      if (socketRef.current.readyState !== WebSocket.OPEN) {
        console.debug(
          'WebSocket is not connected, current state:',
          socketRef.current.readyState,
          'message type:',
          message.type
        );

        if (
          socketRef.current.readyState === WebSocket.CLOSED &&
          autoReconnectRef.current &&
          enabledRef.current
        ) {
          connect();
        }
        return false;
      }

      try {
        const messageString = JSON.stringify(message);
        socketRef.current.send(messageString);
        return true;
      } catch (error) {
        console.error('Error sending WebSocket message:', error);
        return false;
      }
    },
    [connect]
  );

  const sendTextMessage = useCallback(
    (
      conversationId: string,
      content: string,
      metadata?: Record<string, unknown>
    ) => {
      const now = Date.now();
      const lastTimestamp =
        lastMessageTimestampRef.current[conversationId] || 0;

      if (now - lastTimestamp < 100) {
        console.warn('Message sending throttled');
        return false;
      }

      lastMessageTimestampRef.current[conversationId] = now;

      return sendMessage({
        type: 'TEXT_MESSAGE',
        conversationId,
        senderId: userIdRef.current,
        senderType: userTypeRef.current,
        senderName: userNameRef.current,
        content,
        timestamp: new Date().toISOString(),
        metadata,
      });
    },
    [sendMessage]
  );

  const sendTypingIndicator = useCallback(
    (conversationId: string) => {
      const now = Date.now();
      const key = `typing_${conversationId}`;
      const lastTimestamp = lastMessageTimestampRef.current[key] || 0;

      if (now - lastTimestamp < 1000) {
        return false;
      }

      lastMessageTimestampRef.current[key] = now;

      return sendMessage({
        type: 'TYPING_INDICATOR',
        conversationId,
        senderId: userIdRef.current,
        senderType: userTypeRef.current,
        senderName: userNameRef.current,
        timestamp: new Date().toISOString(),
      });
    },
    [sendMessage]
  );

  const sendReadReceipt = useCallback(
    (conversationId: string, messageId?: string) => {
      const message: WebSocketMessage = {
        type: 'READ_RECEIPT',
        conversationId,
        senderId: userIdRef.current,
        senderType: userTypeRef.current,
        senderName: userNameRef.current,
        timestamp: new Date().toISOString(),
      };

      if (messageId) {
        message.messageId = messageId;
      }

      return sendMessage(message);
    },
    [sendMessage]
  );

  const joinConversation = useCallback(
    (conversationId: string) => {
      setActiveConversations(prev => {
        if (prev.includes(conversationId)) {
          return prev;
        }
        return [...prev, conversationId];
      });

      const result = sendMessage({
        type: 'JOIN_CONVERSATION',
        conversationId,
        senderId: userIdRef.current,
        senderType: userTypeRef.current,
        senderName: userNameRef.current,
        timestamp: new Date().toISOString(),
      });

      return result;
    },
    [sendMessage]
  );

  const leaveConversation = useCallback(
    (conversationId: string) => {
      setActiveConversations(prev => prev.filter(id => id !== conversationId));

      return sendMessage({
        type: 'LEAVE_CONVERSATION',
        conversationId,
        senderId: userIdRef.current,
        senderType: userTypeRef.current,
        senderName: userNameRef.current,
        timestamp: new Date().toISOString(),
      });
    },
    [sendMessage]
  );

  const getConversations = useCallback(
    (page = 1, limit = 20, status?: 'active' | 'inactive' | 'archived') => {
      const requestId = Math.random().toString(36).substring(2, 15);
      return sendMessage({
        type: 'GET_CONVERSATIONS',
        requestId,
        page,
        limit,
        status,
        senderId: userIdRef.current,
        senderType: userTypeRef.current,
        timestamp: new Date().toISOString(),
      });
    },
    [sendMessage]
  );

  const getConversation = useCallback(
    (conversationId: string) => {
      const requestId = Math.random().toString(36).substring(2, 15);
      return sendMessage({
        type: 'GET_CONVERSATION',
        conversationId,
        requestId,
        senderId: userIdRef.current,
        senderType: userTypeRef.current,
        timestamp: new Date().toISOString(),
      });
    },
    [sendMessage]
  );

  const createConversation = useCallback(
    (patientId: string, patientName: string, initialMessage?: string) => {
      const requestId = Math.random().toString(36).substring(2, 15);
      return sendMessage({
        type: 'CREATE_CONVERSATION',
        requestId,
        patientId,
        patientName,
        initialMessage,
        senderId: userIdRef.current,
        senderType: userTypeRef.current,
        senderName: userNameRef.current,
        timestamp: new Date().toISOString(),
      });
    },
    [sendMessage]
  );

  const getMessages = useCallback(
    (conversationId: string, page = 1, limit = 50) => {
      const requestId = Math.random().toString(36).substring(2, 15);
      return sendMessage({
        type: 'GET_MESSAGES',
        conversationId,
        requestId,
        page,
        limit,
        senderId: userIdRef.current,
        senderType: userTypeRef.current,
        timestamp: new Date().toISOString(),
      });
    },
    [sendMessage]
  );

  const searchMessages = useCallback(
    (conversationId: string, query: string, page = 1, limit = 20) => {
      const requestId = Math.random().toString(36).substring(2, 15);
      return sendMessage({
        type: 'SEARCH_MESSAGES',
        conversationId,
        requestId,
        query,
        page,
        limit,
        senderId: userIdRef.current,
        senderType: userTypeRef.current,
        timestamp: new Date().toISOString(),
      });
    },
    [sendMessage]
  );

  const markMessagesAsRead = useCallback(
    (conversationId: string) => {
      const requestId = Math.random().toString(36).substring(2, 15);
      return sendMessage({
        type: 'MARK_MESSAGES_READ',
        conversationId,
        requestId,
        senderId: userIdRef.current,
        senderType: userTypeRef.current,
        timestamp: new Date().toISOString(),
      });
    },
    [sendMessage]
  );

  const updateConversationStatus = useCallback(
    (conversationId: string, status: 'active' | 'inactive' | 'archived') => {
      const requestId = Math.random().toString(36).substring(2, 15);
      return sendMessage({
        type: 'UPDATE_CONVERSATION_STATUS',
        conversationId,
        requestId,
        status,
        senderId: userIdRef.current,
        senderType: userTypeRef.current,
        timestamp: new Date().toISOString(),
      });
    },
    [sendMessage]
  );

  const getUnreadCount = useCallback(() => {
    const requestId = Math.random().toString(36).substring(2, 15);
    return sendMessage({
      type: 'GET_UNREAD_COUNT',
      requestId,
      senderId: userIdRef.current,
      senderType: userTypeRef.current,
      timestamp: new Date().toISOString(),
    });
  }, [sendMessage]);

  const getNurseInfo = useCallback(
    (patientId: string) => {
      const requestId = Math.random().toString(36).substring(2, 15);
      return sendMessage({
        type: 'GET_NURSE_INFO',
        requestId,
        patientId,
        senderId: userIdRef.current,
        senderType: userTypeRef.current,
        timestamp: new Date().toISOString(),
      });
    },
    [sendMessage]
  );

  useEffect(() => {
    if (!status.connected) return;

    const pingInterval = setInterval(() => {
      if (socketRef.current?.readyState === WebSocket.OPEN) {
        try {
          socketRef.current.send(JSON.stringify({ type: 'PING' }));
        } catch (error) {
          console.warn('Failed to send ping:', error);
        }
      }
    }, 30000);

    return () => clearInterval(pingInterval);
  }, [status.connected]);

  useEffect(() => {
    if (enabled) {
      const connectTimer = setTimeout(() => {
        if (
          enabledRef.current &&
          !statusRef.current.connected &&
          !statusRef.current.connecting
        ) {
          connect();
        }
      }, 100);

      return () => {
        clearTimeout(connectTimer);
      };
    } else {
      const disconnectTimer = setTimeout(() => {
        disconnect();
      }, 50);

      return () => {
        clearTimeout(disconnectTimer);
      };
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [enabled]);

  useEffect(() => {
    return () => {
      disconnect();
    };
  }, [disconnect]);

  return {
    status,
    connect,
    disconnect,
    sendMessage,
    sendTextMessage,
    sendTypingIndicator,
    sendReadReceipt,
    joinConversation,
    leaveConversation,
    typingUsers,
    activeConversations,

    getConversations,
    getConversation,
    createConversation,
    getMessages,
    searchMessages,
    markMessagesAsRead,
    updateConversationStatus,
    getUnreadCount,
    getNurseInfo,
  };
};

export default useWebSocket;
